import { render, screen } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import muiTheme from '../assets/muiTheme';
import { UserSessionContext } from '../index';
import HeaderNavBar from '../components/layout/HeaderNavBar';
import Applications from '../pages/Applications';
import { CurrentUser } from '../types/user';

// Mock the hooks and components that are not directly related to our test
jest.mock('../hooks/useUserSession');
jest.mock('../hooks/useLocalStorage', () => () => [null, jest.fn()]);
jest.mock('../hooks/useWindowSize', () => () => ({ width: 1024, height: 768 }));
jest.mock('../hooks/useQueryString', () => () => new URLSearchParams());
jest.mock('../components/applications/ApplicationTable', () => () => <div>Application Table</div>);
jest.mock('../components/applications/ProgramTable', () => () => <div>Program Table</div>);
jest.mock('../components/applications/ApplicationFilters', () => () => <div>Application Filters</div>);

const renderWithProviders = (component: React.ReactElement, currentUser: CurrentUser) => {
  return render(
    <ThemeProvider theme={muiTheme}>
      <UserSessionContext.Provider value={currentUser}>
        <BrowserRouter>
          {component}
        </BrowserRouter>
      </UserSessionContext.Provider>
    </ThemeProvider>
  );
};

describe('Client Access Control', () => {
  const baseUser = {
    id: 1,
    primaryClientId: 1,
    clientCreatorId: 1,
    enabled: true,
    name: 'Test User',
    email: '<EMAIL>',
    phone: '************',
    position: 'Test Position',
    location: 'Test Location',
    defaultRole: 'primaryOrganizationalContact' as const,
    applicationClients: [],
    awardClients: [],
    primary: false,
    isMillenniumUser: false,
    isMillenniumAnalyst: false,
  };

  describe('HeaderNavBar', () => {
    it('should show "Programs" for flex clients', () => {
      const flexUser: CurrentUser = {
        ...baseUser,
        userType: 'userAdmin',
        clientCreator: [{
          id: 1,
          name: 'Flex Client',
          clientType: 'Flex Client',
          billingType: 'Flex',
          population: 'Test',
          state: 'Test',
          customFields: '[]',
          awardsEnabled: false,
          privateAwardsManagement: false,
          enabled: true,
          createdAt: '2023-01-01',
          updatedAt: '2023-01-01',
          canCreateAward: false,
          contractStartedAt: null,
          contractEndsAt: null,
        }],
      };

      renderWithProviders(<HeaderNavBar />, flexUser);
      
      expect(screen.getByText('Programs')).toBeInTheDocument();
      expect(screen.queryByText('Programs & Apps')).not.toBeInTheDocument();
    });

    it('should show "Programs & Apps" for full clients', () => {
      const fullUser: CurrentUser = {
        ...baseUser,
        userType: 'userAdmin',
        clientCreator: [{
          id: 1,
          name: 'Full Client',
          clientType: 'Full Client',
          billingType: 'Full',
          population: 'Test',
          state: 'Test',
          customFields: '[]',
          awardsEnabled: false,
          privateAwardsManagement: false,
          enabled: true,
          createdAt: '2023-01-01',
          updatedAt: '2023-01-01',
          canCreateAward: false,
          contractStartedAt: null,
          contractEndsAt: null,
        }],
      };

      renderWithProviders(<HeaderNavBar />, fullUser);
      
      expect(screen.getByText('Programs & Apps')).toBeInTheDocument();
      expect(screen.queryByText('Programs')).not.toBeInTheDocument();
    });

    it('should show "Programs & Apps" for millennium users', () => {
      const millenniumUser: CurrentUser = {
        ...baseUser,
        userType: 'millenniumAdmin',
        isMillenniumUser: true,
      };

      renderWithProviders(<HeaderNavBar />, millenniumUser);
      
      expect(screen.getByText('Programs & Apps')).toBeInTheDocument();
    });
  });

  describe('Applications Page Access', () => {
    it('should restrict flex clients to programs tab only', () => {
      const flexUser: CurrentUser = {
        ...baseUser,
        userType: 'userAdmin',
        clientCreator: [{
          id: 1,
          name: 'Flex Client',
          clientType: 'Flex Client',
          billingType: 'Flex',
          population: 'Test',
          state: 'Test',
          customFields: '[]',
          awardsEnabled: false,
          privateAwardsManagement: false,
          enabled: true,
          createdAt: '2023-01-01',
          updatedAt: '2023-01-01',
          canCreateAward: false,
          contractStartedAt: null,
          contractEndsAt: null,
        }],
      };

      // Mock the location to simulate being on applications page
      Object.defineProperty(window, 'location', {
        value: { pathname: '/dashboard/applications' },
        writable: true,
      });

      renderWithProviders(<Applications />, flexUser);
      
      // The component should render (flex clients can access the page but only see programs)
      expect(screen.getByText('Program Table')).toBeInTheDocument();
    });

    it('should allow full clients access to both programs and applications', () => {
      const fullUser: CurrentUser = {
        ...baseUser,
        userType: 'userAdmin',
        clientCreator: [{
          id: 1,
          name: 'Full Client',
          clientType: 'Full Client',
          billingType: 'Full',
          population: 'Test',
          state: 'Test',
          customFields: '[]',
          awardsEnabled: false,
          privateAwardsManagement: false,
          enabled: true,
          createdAt: '2023-01-01',
          updatedAt: '2023-01-01',
          canCreateAward: false,
          contractStartedAt: null,
          contractEndsAt: null,
        }],
      };

      Object.defineProperty(window, 'location', {
        value: { pathname: '/dashboard/applications' },
        writable: true,
      });

      renderWithProviders(<Applications />, fullUser);
      
      // Full clients should be able to access both programs and applications
      expect(screen.getByText('Application Table')).toBeInTheDocument();
    });
  });

  // Backend API tests for client access control
  describe('Backend Client Access Control', () => {
    describe('Application API Access', () => {
      it('should allow full client users to access applications API', async () => {
        // This would require setting up a test user with full client billing type
        // and testing the /applications endpoint
        // Implementation would depend on the existing test setup
        expect(true).toBe(true); // Placeholder for actual implementation
      });

      it('should allow flex client users to access applications API (they get filtered data)', async () => {
        // Flex clients can access the API but get filtered results
        // The filtering happens in the frontend logic, not API restrictions
        expect(true).toBe(true); // Placeholder for actual implementation
      });
    });
  });
});
